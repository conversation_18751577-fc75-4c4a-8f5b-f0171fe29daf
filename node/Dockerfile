FROM ubuntu:20.04

# 避免交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 安装必要的依赖
RUN apt-get update && apt-get install -y \
    bash \
    curl \
    git \
    python2 \
    g++ \
    make \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /opt/nvm

# 安装nvm
ENV NVM_DIR=/opt/nvm
ENV NVM_VERSION=v0.39.7

RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/${NVM_VERSION}/install.sh | bash \
    && . $NVM_DIR/nvm.sh \
    && echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> /root/.bashrc \
    && echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> /root/.bashrc \
    && chmod -R 777 $NVM_DIR

# 设置NODE版本
ENV NODE_VERSIONS="12.12.0 14.21.3 16.20.2 18.20.8 20.19.2 22.16.0"
ENV DEFAULT_NODE_VERSION=16.20.2

# 为Node.js 12.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 12.12.0 --no-progress \
    && nvm use 12.12.0 \
    && chmod -R 777 $NVM_DIR/versions/node/v12.12.0 \
    && npm install -g yarn@1.22.19 grunt-cli@1.3.2 gulp-cli@2.2.0 vite@2.9.15 \
    && npm cache clean --force

# 为Node.js 14.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 14.21.3 --no-progress \
    && nvm use 14.21.3 \
    && chmod -R 777 $NVM_DIR/versions/node/v14.21.3 \
    && npm install -g yarn@1.22.19 grunt-cli@1.4.3 gulp-cli@2.3.0 vite@3.2.7 \
    && npm cache clean --force

# 为Node.js 16.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 16.20.2 --no-progress \
    && nvm use 16.20.2 \
    && chmod -R 777 $NVM_DIR/versions/node/v16.20.2 \
    && npm install -g yarn@1.22.19 grunt-cli@1.4.3 gulp-cli@2.3.0 vite@4.5.2 \
    && npm cache clean --force

# 为Node.js 18.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 18.20.8 --no-progress \
    && nvm use 18.20.8 \
    && chmod -R 777 $NVM_DIR/versions/node/v18.20.8 \
    && npm install -g yarn@1.22.22 grunt-cli@1.4.3 gulp-cli@2.3.0 vite@5.1.4 \
    && npm cache clean --force

# 为Node.js 20.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 20.19.2 --no-progress \
    && nvm use 20.19.2 \
    && chmod -R 777 $NVM_DIR/versions/node/v20.19.2 \
    && npm install -g yarn@1.22.22 grunt-cli@1.4.3 gulp-cli@2.3.0 vite@5.2.6 \
    && npm cache clean --force

# 为Node.js 22.x安装兼容版本的工具
RUN . $NVM_DIR/nvm.sh \
    && nvm install 22.16.0 --no-progress \
    && nvm use 22.16.0 \
    && chmod -R 777 $NVM_DIR/versions/node/v22.16.0 \
    && npm install -g yarn@1.22.22 grunt-cli@1.4.3 gulp-cli@2.3.0 vite@5.2.6 \
    && npm cache clean --force

# 设置默认Node.js版本
RUN . $NVM_DIR/nvm.sh \
    && nvm alias default $DEFAULT_NODE_VERSION \
    && nvm use default

# 清理构建缓存和不必要的文件以减小镜像体积
RUN rm -rf /root/.npm /tmp/* \
    && find $NVM_DIR/versions/node -name "*.md" -delete \
    && find $NVM_DIR/versions/node -name "LICENSE" -delete \
    && find $NVM_DIR/versions/node -name "CHANGELOG.md" -delete

# 设置环境变量使nvm可用
ENV PATH=$NVM_DIR/versions/node/v$DEFAULT_NODE_VERSION/bin:$PATH

# 创建nvm切换脚本，方便切换Node.js版本
RUN echo '#!/bin/bash\n\
if [ -z "$1" ]; then\n\
  echo "Please provide a Node.js version"\n\
  echo "Available versions: $(ls -1 /opt/nvm/versions/node | sed \"s/v//g\")"\n\
  exit 1\n\
fi\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
nvm use $1\n\
export PATH=$NVM_DIR/versions/node/v$1/bin:$PATH\n\
echo "Switched to Node.js $1"\n\
echo "Yarn version: $(yarn --version)"\n\
echo "Grunt version: $(grunt --version | head -n 1)"\n\
echo "Gulp version: $(gulp --version)"\n\
echo "Vite version: $(vite --version)"' > /usr/local/bin/nodeswitch \
    && chmod +x /usr/local/bin/nodeswitch

# 创建全局nvm环境配置文件，确保在所有shell环境中都可用
RUN echo 'export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' > /etc/profile.d/nvm.sh \
    && chmod +x /etc/profile.d/nvm.sh

# 创建nvm命令的符号链接，方便Jenkins直接调用
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
# 调用nvm函数而不是命令，避免递归调用\n\
if declare -f nvm > /dev/null; then\n\
  nvm "$@"\n\
else\n\
  echo "Error: nvm function not found" >&2\n\
  exit 1\n\
fi' > /usr/local/bin/nvm \
    && chmod +x /usr/local/bin/nvm

# 创建node和npm的符号链接，确保Jenkins能找到当前版本的node和npm
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
node "$@"' > /usr/local/bin/node \
    && chmod +x /usr/local/bin/node

RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
npm "$@"' > /usr/local/bin/npm \
    && chmod +x /usr/local/bin/npm

RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
yarn "$@"' > /usr/local/bin/yarn \
    && chmod +x /usr/local/bin/yarn

# 创建grunt命令的符号链接
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
grunt "$@"' > /usr/local/bin/grunt \
    && chmod +x /usr/local/bin/grunt

# 创建gulp命令的符号链接
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
gulp "$@"' > /usr/local/bin/gulp \
    && chmod +x /usr/local/bin/gulp

# 创建vite命令的符号链接
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
vite "$@"' > /usr/local/bin/vite \
    && chmod +x /usr/local/bin/vite

# 创建npx命令的符号链接
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
npx "$@"' > /usr/local/bin/npx \
    && chmod +x /usr/local/bin/npx

# 创建启动脚本，确保nvm在每次容器启动时都可用
RUN echo '#!/bin/bash\n\
export NVM_DIR=/opt/nvm\n\
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"\n\
exec "$@"' > /usr/local/bin/docker-entrypoint.sh \
    && chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["bash"]