# 构建阶段：安装编译工具和phpbrew
FROM centos:7 AS builder

# 替换为阿里云镜像源
RUN sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/CentOS-* && \
    sed -i 's|^#baseurl=http://mirror.centos.org|baseurl=https://mirrors.aliyun.com|g' /etc/yum.repos.d/CentOS-*

# 添加 EPEL 和 Remi 仓库
RUN yum install -y epel-release && \
    yum install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm

# 安装基础PHP（用于运行phpbrew）
RUN yum install -y --enablerepo=remi-php74 \
    php74 \
    php74-php-cli \
    php74-php-common \
    php74-php-json \
    php74-php-openssl \
    php74-php-xml \
    php74-php-mbstring \
    php74-php-tokenizer \
    php74-php-phar

# 设置PHP 7.4为默认PHP
RUN ln -sf /usr/bin/php74 /usr/bin/php

# 安装编译PHP所需的依赖
RUN yum groupinstall "Development Tools" -y && \
    yum install -y \
    curl \
    wget \
    git \
    libxml2-devel \
    libcurl-devel \
    libjpeg-turbo-devel \
    libpng-devel \
    freetype-devel \
    bzip2-devel \
    readline-devel \
    sqlite-devel \
    zlib-devel \
    autoconf \
    automake \
    libtool \
    oniguruma-devel \
    libicu-devel \
    libxslt-devel \
    re2c \
    libmcrypt-devel \
    libedit-devel \
    openssl-devel \
    libxml2-static \
    zlib-static \
    && yum clean all && \
    rm -rf /var/cache/yum

# 安装PHP 7.3和PHP 8.0需要的额外依赖
RUN yum install -y --enablerepo=remi libzip5 openssl11 openssl11-devel

# 安装libzip5-devel（单独安装以避免冲突）
RUN yum install -y --enablerepo=remi --disablerepo=base,updates libzip5-devel

# 下载并安装phpbrew
COPY phpbrew.phar /usr/local/bin/phpbrew
RUN chmod +x /usr/local/bin/phpbrew

# 确保phpbrew环境文件正确生成
RUN mkdir -p /opt/phpbrew && \
    export PHPBREW_ROOT=/opt/phpbrew && \
    export PHPBREW_HOME=/root/.phpbrew && \
    # 初始化phpbrew
    phpbrew init --root=/opt/phpbrew && \
    # 确保bashrc文件存在
    [ -f /root/.phpbrew/bashrc ] || phpbrew init --root=/opt/phpbrew && \
    # 修复bashrc文件内容
    sed -i 's|source "$PHPBREW_ROOT/bashrc"|# Removed potentially broken source line|' /root/.phpbrew/bashrc && \
    # 添加环境变量到bashrc
    echo 'export PHPBREW_ROOT=/opt/phpbrew' >> /root/.bashrc && \
    echo 'export PHPBREW_HOME=/root/.phpbrew' >> /root/.bashrc && \
    echo 'source $PHPBREW_HOME/bashrc' >> /root/.bashrc

# 直接下载和编译PHP
RUN mkdir -p /tmp/php-build && cd /tmp/php-build && \
    # 下载PHP 7.3.33
    curl -L -o php-7.3.33.tar.gz https://www.php.net/distributions/php-7.3.33.tar.gz && \
    tar xzf php-7.3.33.tar.gz && \
    cd php-7.3.33 && \
    ./configure \
        --prefix=/opt/phpbrew/php/php-7.3.33 \
        --with-config-file-path=/opt/phpbrew/php/php-7.3.33/etc \
        --with-config-file-scan-dir=/opt/phpbrew/php/php-7.3.33/etc/conf.d \
        --enable-bcmath \
        --enable-calendar \
        --enable-dba \
        --enable-exif \
        --enable-ftp \
        --enable-gd \
        --enable-intl \
        --enable-mbstring \
        --enable-pcntl \
        --enable-soap \
        --enable-sockets \
        --with-curl \
        --with-gettext \
        --with-mysqli=mysqlnd \
        --with-pdo-mysql=mysqlnd \
        --with-openssl \
        --with-readline \
        --with-zlib \
        --with-zip \
        --enable-fpm \
        --enable-opcache && \
    make -j$(nproc) && \
    make install && \
    # 创建配置文件目录
    mkdir -p /opt/phpbrew/php/php-7.3.33/etc/conf.d && \
    # 安装Composer
    curl -sS https://getcomposer.org/installer | php -- --install-dir=/opt/phpbrew/php/php-7.3.33/bin --filename=composer && \
    # 清理
    cd /tmp && rm -rf /tmp/php-build

# 下载和编译PHP 8.0.30
RUN mkdir -p /tmp/php-build && cd /tmp/php-build && \
    curl -L -o php-8.0.30.tar.gz https://www.php.net/distributions/php-8.0.30.tar.gz && \
    tar xzf php-8.0.30.tar.gz && \
    cd php-8.0.30 && \
    ./configure \
        --prefix=/opt/phpbrew/php/php-8.0.30 \
        --with-config-file-path=/opt/phpbrew/php/php-8.0.30/etc \
        --with-config-file-scan-dir=/opt/phpbrew/php/php-8.0.30/etc/conf.d \
        --enable-bcmath \
        --enable-calendar \
        --enable-dba \
        --enable-exif \
        --enable-ftp \
        --enable-gd \
        --enable-intl \
        --enable-mbstring \
        --enable-pcntl \
        --enable-soap \
        --enable-sockets \
        --with-curl \
        --with-gettext \
        --with-mysqli=mysqlnd \
        --with-pdo-mysql=mysqlnd \
        --with-openssl \
        --with-readline \
        --with-zlib \
        --with-zip \
        --enable-fpm \
        --enable-opcache && \
    make -j$(nproc) && \
    make install && \
    # 创建配置文件目录
    mkdir -p /opt/phpbrew/php/php-8.0.30/etc/conf.d && \
    # 安装Composer
    curl -sS https://getcomposer.org/installer | php -- --install-dir=/opt/phpbrew/php/php-8.0.30/bin --filename=composer && \
    # 清理
    cd /tmp && rm -rf /tmp/php-build

# 创建phpbrew版本记录文件
RUN mkdir -p /opt/phpbrew/php && \
    echo "7.3.33" > /opt/phpbrew/php/php-7.3.33/.phpbrew.php && \
    echo "8.0.30" > /opt/phpbrew/php/php-8.0.30/.phpbrew.php && \
    # 创建phpbrew的版本记录
    mkdir -p /opt/phpbrew/build && \
    echo "7.3.33" >> /opt/phpbrew/build/php-7.3.33.built && \
    echo "8.0.30" >> /opt/phpbrew/build/php-8.0.30.built && \
    # 创建phpbrew的当前版本文件
    echo "8.0.30" > /root/.phpbrew/php.version && \
    echo "8.0.30" > /root/.phpbrew/current

# 最终镜像阶段
FROM centos:7

# 替换为阿里云镜像源
RUN sed -i 's|^mirrorlist=|#mirrorlist=|g' /etc/yum.repos.d/CentOS-* && \
    sed -i 's|^#baseurl=http://mirror.centos.org|baseurl=https://mirrors.aliyun.com|g' /etc/yum.repos.d/CentOS-*

# 添加EPEL和Remi仓库
RUN yum install -y epel-release && \
    yum install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm

# 安装运行时依赖
RUN yum update -y && \
    yum install -y --enablerepo=remi \
    libxml2 \
    libcurl \
    libjpeg-turbo \
    libpng \
    freetype \
    bzip2 \
    readline \
    sqlite \
    zlib \
    oniguruma \
    libicu \
    libxslt \
    libzip5 \
    openssl11 \
    libmcrypt \
    libedit \
    openssl \
    git \
    unzip \
    which \
    && yum clean all && \
    rm -rf /var/cache/yum

# 从构建阶段复制phpbrew和已安装的PHP
COPY --from=builder /usr/local/bin/phpbrew /usr/local/bin/
COPY --from=builder /opt/phpbrew /opt/phpbrew
COPY --from=builder /root/.phpbrew /root/.phpbrew
COPY --from=builder /root/.bashrc /root/.bashrc

# 关键修复：确保bashrc文件存在且内容正确
RUN if [ ! -f /root/.phpbrew/bashrc ]; then \
      echo "Creating missing bashrc file"; \
      { \
        echo '# Auto-generated bashrc for phpbrew'; \
        echo 'export PHPBREW_ROOT=/opt/phpbrew'; \
        echo 'export PHPBREW_HOME=/root/.phpbrew'; \
        echo 'export PATH=$PHPBREW_ROOT/bin:$PHPBREW_ROOT/php/php-$PHP_VERSION/bin:$PATH'; \
        echo 'function phpbrew() {'; \
        echo '  /usr/local/bin/phpbrew "$@"'; \
        echo '}'; \
      } > /root/.phpbrew/bashrc; \
    fi && \
    # 修复可能存在的错误源引用
    sed -i 's|source "$PHPBREW_ROOT/bashrc"|# Removed potentially broken source line|' /root/.phpbrew/bashrc

# 配置环境
ENV PHPBREW_ROOT=/opt/phpbrew
ENV PHPBREW_HOME=/root/.phpbrew
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV COMPOSER_MIRROR=https://mirrors.aliyun.com/composer/
ENV PHP_VERSION=8.0.30
ENV PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION}/bin:$PATH"

# 创建PHP版本切换脚本
RUN echo '#!/bin/bash' > /usr/local/bin/switch-php && \
    echo 'if [ -n "$1" ]; then' >> /usr/local/bin/switch-php && \
    echo '  source /root/.bashrc' >> /usr/local/bin/switch-php && \
    echo '  source /root/.phpbrew/bashrc' >> /usr/local/bin/switch-php && \
    echo '  phpbrew switch $1' >> /usr/local/bin/switch-php && \
    echo '  echo "Switched to PHP $1"' >> /usr/local/bin/switch-php && \
    echo '  php -v' >> /usr/local/bin/switch-php && \
    echo 'else' >> /usr/local/bin/switch-php && \
    echo '  echo "Usage: switch-php <version>"' >> /usr/local/bin/switch-php && \
    echo '  echo "Available versions:"' >> /usr/local/bin/switch-php && \
    echo '  source /root/.bashrc' >> /usr/local/bin/switch-php && \
    echo '  phpbrew list' >> /usr/local/bin/switch-php && \
    echo 'fi' >> /usr/local/bin/switch-php && \
    chmod +x /usr/local/bin/switch-php

# 创建Composer镜像源设置脚本
RUN echo '#!/bin/bash' > /usr/local/bin/set-composer-mirror && \
    echo 'if [ -z "$1" ]; then' >> /usr/local/bin/set-composer-mirror && \
    echo '  echo "Error: No composer mirror URL provided"' >> /usr/local/bin/set-composer-mirror && \
    echo '  echo "Usage: set-composer-mirror <mirror-url>"' >> /usr/local/bin/set-composer-mirror && \
    echo '  exit 1' >> /usr/local/bin/set-composer-mirror && \
    echo 'fi' >> /usr/local/bin/set-composer-mirror && \
    echo '' >> /usr/local/bin/set-composer-mirror && \
    echo '# 设置环境变量' >> /usr/local/bin/set-composer-mirror && \
    echo 'export COMPOSER_MIRROR="$1"' >> /usr/local/bin/set-composer-mirror && \
    echo 'echo "Setting Composer mirror to: $1"' >> /usr/local/bin/set-composer-mirror && \
    echo '' >> /usr/local/bin/set-composer-mirror && \
    echo '# 为当前会话和子进程设置COMPOSER_MIRROR' >> /usr/local/bin/set-composer-mirror && \
    echo 'if [ -f /etc/environment ]; then' >> /usr/local/bin/set-composer-mirror && \
    echo '  # 移除旧的COMPOSER_MIRROR设置' >> /usr/local/bin/set-composer-mirror && \
    echo '  sed -i "/^COMPOSER_MIRROR=/d" /etc/environment' >> /usr/local/bin/set-composer-mirror && \
    echo '  # 添加新的COMPOSER_MIRROR设置' >> /usr/local/bin/set-composer-mirror && \
    echo '  echo "COMPOSER_MIRROR=$1" >> /etc/environment' >> /usr/local/bin/set-composer-mirror && \
    echo 'fi' >> /usr/local/bin/set-composer-mirror && \
    echo '' >> /usr/local/bin/set-composer-mirror && \
    echo '# 如果存在.bashrc文件，也在其中设置环境变量' >> /usr/local/bin/set-composer-mirror && \
    echo 'if [ -f ~/.bashrc ]; then' >> /usr/local/bin/set-composer-mirror && \
    echo '  # 移除旧的COMPOSER_MIRROR设置' >> /usr/local/bin/set-composer-mirror && \
    echo '  sed -i "/^export COMPOSER_MIRROR=/d" ~/.bashrc' >> /usr/local/bin/set-composer-mirror && \
    echo '  # 添加新的COMPOSER_MIRROR设置' >> /usr/local/bin/set-composer-mirror && \
    echo '  echo "export COMPOSER_MIRROR=$1" >> ~/.bashrc' >> /usr/local/bin/set-composer-mirror && \
    echo 'fi' >> /usr/local/bin/set-composer-mirror && \
    echo '' >> /usr/local/bin/set-composer-mirror && \
    echo '# 为所有已安装的PHP版本配置Composer' >> /usr/local/bin/set-composer-mirror && \
    echo 'source /root/.bashrc' >> /usr/local/bin/set-composer-mirror && \
    echo 'for version in $(phpbrew list | grep -o "[0-9]\+\.[0-9]\+\.[0-9]\+"); do' >> /usr/local/bin/set-composer-mirror && \
    echo '  phpbrew use $version' >> /usr/local/bin/set-composer-mirror && \
    echo '  echo "Configuring Composer for PHP $version"' >> /usr/local/bin/set-composer-mirror && \
    echo '  composer config -g repo.packagist composer $1' >> /usr/local/bin/set-composer-mirror && \
    echo 'done' >> /usr/local/bin/set-composer-mirror && \
    echo '' >> /usr/local/bin/set-composer-mirror && \
    echo 'echo "Composer mirror has been successfully set to: $1"' >> /usr/local/bin/set-composer-mirror && \
    chmod +x /usr/local/bin/set-composer-mirror

# 创建php全局命令包装器，确保在Jenkins中可用
RUN echo '#!/bin/bash' > /usr/local/bin/php-wrapper && \
    echo 'export PHPBREW_ROOT=/opt/phpbrew' >> /usr/local/bin/php-wrapper && \
    echo 'export PHPBREW_HOME=/root/.phpbrew' >> /usr/local/bin/php-wrapper && \
    echo 'source $PHPBREW_HOME/bashrc >/dev/null 2>&1 || true' >> /usr/local/bin/php-wrapper && \
    echo 'export PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin:$PATH"' >> /usr/local/bin/php-wrapper && \
    echo 'if [ -n "$PHPBREW_PHP" ]; then' >> /usr/local/bin/php-wrapper && \
    echo '  "${PHPBREW_ROOT}/php/php-${PHPBREW_PHP}/bin/php" "$@"' >> /usr/local/bin/php-wrapper && \
    echo 'else' >> /usr/local/bin/php-wrapper && \
    echo '  "${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin/php" "$@"' >> /usr/local/bin/php-wrapper && \
    echo 'fi' >> /usr/local/bin/php-wrapper && \
    chmod +x /usr/local/bin/php-wrapper && \
    # 备份原始php命令（如果存在）并创建符号链接到标准位置
    if [ -f /usr/bin/php ]; then mv /usr/bin/php /usr/bin/php-original; fi && \
    ln -sf /usr/local/bin/php-wrapper /usr/bin/php

# 创建composer全局命令包装器，确保在Jenkins中可用
RUN echo '#!/bin/bash' > /usr/local/bin/composer-wrapper && \
    echo 'export PHPBREW_ROOT=/opt/phpbrew' >> /usr/local/bin/composer-wrapper && \
    echo 'export PHPBREW_HOME=/root/.phpbrew' >> /usr/local/bin/composer-wrapper && \
    echo 'source $PHPBREW_HOME/bashrc >/dev/null 2>&1 || true' >> /usr/local/bin/composer-wrapper && \
    echo 'export PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin:$PATH"' >> /usr/local/bin/composer-wrapper && \
    echo 'if [ -n "$PHPBREW_PHP" ]; then' >> /usr/local/bin/composer-wrapper && \
    echo '  "${PHPBREW_ROOT}/php/php-${PHPBREW_PHP}/bin/composer" "$@"' >> /usr/local/bin/composer-wrapper && \
    echo 'else' >> /usr/local/bin/composer-wrapper && \
    echo '  "${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin/composer" "$@"' >> /usr/local/bin/composer-wrapper && \
    echo 'fi' >> /usr/local/bin/composer-wrapper && \
    chmod +x /usr/local/bin/composer-wrapper && \
    # 备份原始composer命令（如果存在）并创建符号链接
    if [ -f /usr/local/bin/composer ]; then mv /usr/local/bin/composer /usr/local/bin/composer-original; fi && \
    ln -sf /usr/local/bin/composer-wrapper /usr/local/bin/composer

# 创建全局环境配置文件，确保在所有shell环境中都可用
RUN echo 'export PHPBREW_ROOT=/opt/phpbrew' > /etc/profile.d/phpbrew.sh && \
    echo 'export PHPBREW_HOME=/root/.phpbrew' >> /etc/profile.d/phpbrew.sh && \
    echo 'export PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin:$PATH"' >> /etc/profile.d/phpbrew.sh && \
    echo '[ -s "$PHPBREW_HOME/bashrc" ] && source "$PHPBREW_HOME/bashrc" || true' >> /etc/profile.d/phpbrew.sh && \
    chmod +x /etc/profile.d/phpbrew.sh

# 创建入口点脚本
RUN echo '#!/bin/bash' > /usr/local/bin/entrypoint.sh && \
    echo '# 直接加载phpbrew环境' >> /usr/local/bin/entrypoint.sh && \
    echo 'source /root/.bashrc >/dev/null 2>&1 || true' >> /usr/local/bin/entrypoint.sh && \
    echo 'source /root/.phpbrew/bashrc >/dev/null 2>&1 || true' >> /usr/local/bin/entrypoint.sh && \
    echo '' >> /usr/local/bin/entrypoint.sh && \
    echo '# 手动设置PATH确保phpbrew可用' >> /usr/local/bin/entrypoint.sh && \
    echo 'export PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION:-8.0.30}/bin:$PATH"' >> /usr/local/bin/entrypoint.sh && \
    echo '' >> /usr/local/bin/entrypoint.sh && \
    echo '# 切换到指定的PHP版本' >> /usr/local/bin/entrypoint.sh && \
    echo 'if [ -n "$PHP_VERSION" ]; then' >> /usr/local/bin/entrypoint.sh && \
    echo '  /usr/local/bin/phpbrew switch "$PHP_VERSION" >/dev/null 2>&1 || true' >> /usr/local/bin/entrypoint.sh && \
    echo '  export PATH="${PHPBREW_ROOT}/bin:${PHPBREW_ROOT}/php/php-${PHP_VERSION}/bin:$PATH"' >> /usr/local/bin/entrypoint.sh && \
    echo '  echo "Using PHP version: $PHP_VERSION"' >> /usr/local/bin/entrypoint.sh && \
    echo '  php -v' >> /usr/local/bin/entrypoint.sh && \
    echo 'fi' >> /usr/local/bin/entrypoint.sh && \
    echo '' >> /usr/local/bin/entrypoint.sh && \
    echo '# 设置Composer镜像源' >> /usr/local/bin/entrypoint.sh && \
    echo 'if [ -n "$COMPOSER_MIRROR" ]; then' >> /usr/local/bin/entrypoint.sh && \
    echo '  echo "Setting Composer mirror to: $COMPOSER_MIRROR"' >> /usr/local/bin/entrypoint.sh && \
    echo '  composer config -g repo.packagist composer "$COMPOSER_MIRROR"' >> /usr/local/bin/entrypoint.sh && \
    echo 'fi' >> /usr/local/bin/entrypoint.sh && \
    echo '' >> /usr/local/bin/entrypoint.sh && \
    echo '# 执行传入的命令' >> /usr/local/bin/entrypoint.sh && \
    echo 'if [ $# -eq 0 ]; then' >> /usr/local/bin/entrypoint.sh && \
    echo '  exec /bin/bash' >> /usr/local/bin/entrypoint.sh && \
    echo 'else' >> /usr/local/bin/entrypoint.sh && \
    echo '  exec "$@"' >> /usr/local/bin/entrypoint.sh && \
    echo 'fi' >> /usr/local/bin/entrypoint.sh && \
    chmod +x /usr/local/bin/entrypoint.sh

# 创建版本信息脚本
RUN echo '#!/bin/bash' > /usr/local/bin/versions && \
    echo 'source /root/.bashrc' >> /usr/local/bin/versions && \
    echo 'echo "Available PHP versions:"' >> /usr/local/bin/versions && \
    echo 'phpbrew list' >> /usr/local/bin/versions && \
    echo 'echo -e "\nCurrent PHP version:"' >> /usr/local/bin/versions && \
    echo 'php -v' >> /usr/local/bin/versions && \
    echo 'echo -e "\nComposer version:"' >> /usr/local/bin/versions && \
    echo 'composer --version' >> /usr/local/bin/versions && \
    echo 'echo -e "\nCurrent Composer mirror:"' >> /usr/local/bin/versions && \
    echo 'composer config -g repo.packagist.composer' >> /usr/local/bin/versions && \
    chmod +x /usr/local/bin/versions

# 设置工作目录
WORKDIR /app

# 设置入口点
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["/bin/bash"]
