FROM php:7.3.3-cli-alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装基本依赖
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    oniguruma-dev \
    freetype-dev \
    libjpeg-turbo-dev

# 安装PHP扩展
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-install -j$(nproc) \
    pdo_mysql \
    mysqli \
    gd \
    bcmath \
    zip \
    opcache \
    soap \
    mbstring

# 安装Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# 设置Composer中国镜像，使用环境变量
RUN composer config -g secure-http false
RUN composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/

# 清理缓存
RUN rm -rf /tmp/* /var/cache/apk/*

WORKDIR /var/www/html

# 设置默认命令
CMD ["php", "-a"]