OpenCV-Python Tutorials {#tutorial_py_root}
=======================

-   @subpage tutorial_py_table_of_contents_setup

    Learn how to setup OpenCV-Python on your computer!

-   @subpage tutorial_py_table_of_contents_gui

    Here you will learn how to display and save images and videos, control mouse events and create trackbar.

-   @subpage tutorial_py_table_of_contents_core

    In this section you
    will learn basic operations on image like pixel editing, geometric transformations, code
    optimization, some mathematical tools etc.

-   @subpage tutorial_py_table_of_contents_imgproc

    In this section
    you will learn different image processing functions inside OpenCV.

-   @subpage tutorial_py_table_of_contents_feature2d

    In this section
    you will learn about feature detectors and descriptors

-   @ref tutorial_table_of_content_video

    In this section you
    will learn different techniques to work with videos like object tracking etc.

-   @subpage tutorial_py_table_of_contents_calib3d

    In this section we
    will learn about camera calibration, stereo imaging etc.

-   @subpage tutorial_py_table_of_contents_ml

    In this section you
    will learn different image processing functions inside OpenCV.

-   @subpage tutorial_py_table_of_contents_photo

    In this section you
    will learn different computational photography techniques like image denoising etc.

-   @ref tutorial_table_of_content_objdetect

    In this section you
    will learn object detection techniques like face detection etc.

-   @subpage tutorial_py_table_of_contents_bindings

    In this section, we will see how OpenCV-Python bindings are generated
