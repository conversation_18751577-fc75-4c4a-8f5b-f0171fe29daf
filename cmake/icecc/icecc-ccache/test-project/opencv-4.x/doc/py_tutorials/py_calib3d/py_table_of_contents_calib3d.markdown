Camera Calibration and 3D Reconstruction {#tutorial_py_table_of_contents_calib3d}
========================================

-   @subpage tutorial_py_calibration

    Let's find how good
    is our camera. Is there any distortion in images taken with it? If so how to correct it?

-   @subpage tutorial_py_pose

    This is a small
    section which will help you to create some cool 3D effects with calib module.

-   @subpage tutorial_py_epipolar_geometry

    Let's understand
    epipolar geometry and epipolar constraint.

-   @subpage tutorial_py_depthmap

    Extract depth
    information from 2D images.
