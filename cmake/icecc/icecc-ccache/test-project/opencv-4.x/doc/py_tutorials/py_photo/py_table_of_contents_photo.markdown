Computational Photography {#tutorial_py_table_of_contents_photo}
=========================

Here you will learn different OpenCV functionalities related to Computational Photography like image
denoising etc.

-   @subpage tutorial_py_non_local_means

    See a good technique
    to remove noises in images called Non-Local Means Denoising

-   @subpage tutorial_py_inpainting

    Do you have a old
    degraded photo with many black spots and strokes on it? Take it. Let's try to restore them with a
    technique called image inpainting.

-   @subpage tutorial_py_hdr

    Learn how to merge exposure sequence and process high dynamic range images.
