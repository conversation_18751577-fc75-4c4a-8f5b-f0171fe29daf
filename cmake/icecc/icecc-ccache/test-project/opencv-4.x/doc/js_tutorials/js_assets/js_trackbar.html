<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Trackbar Example</title>
<link href="js_example_style.css" rel="stylesheet" type="text/css" />
</head>
<body>
<h2>Trackbar Example</h2>
<p>
    &lt;canvas&gt; elements named <b>canvasInput1</b>, <b>canvasInput2</b> and <b>canvasOutput</b> have been prepared.<br>
    The code of &lt;textarea&gt; will be executed when &lt;input&gt; element named <b>trackbar</b> value changes.<br>
    You can change the code in the &lt;textarea&gt; to investigate more.
</p>
<div>
<textarea class="code" rows="12" cols="80" id="codeEditor" spellcheck="false">
</textarea>
<p class="err" id="errorMessage"></p>
</div>
<div>
    <b>trackbar</b>
    <input type="range" id="trackbar" disabled value="50" min="0" max="100" step="1">
    <label id="weightValue" ></label>
    <div>
        <table cellpadding="0" cellspacing="0" width="0" border="0">
        <tr>
            <td>
                <canvas id="canvasInput1" class="small"></canvas>
            </td>
            <td>
                <canvas id="canvasInput2" class="small"></canvas>
            </td>
            <td>
                <canvas id="canvasOutput" class="small"></canvas>
            </td>
        </tr>
        <tr>
            <td>
                <div class="caption">canvasInput1</div>
            </td>
            <td>
                <div class="caption">canvasInput2</div>
            </td>
            <td>
                <div class="caption">canvasOutput</div>
            </td>
        </tr>
        </table>
    </div>
</div>
<script src="utils.js" type="text/javascript"></script>
<script id="codeSnippet" type="text/code-snippet">
let trackbar = document.getElementById('trackbar');
let alpha = trackbar.value/trackbar.max;
let beta = ( 1.0 - alpha );
let src1 = cv.imread('canvasInput1');
let src2 = cv.imread('canvasInput2');
let dst = new cv.Mat();
cv.addWeighted( src1, alpha, src2, beta, 0.0, dst, -1);
cv.imshow('canvasOutput', dst);
dst.delete();
src1.delete();
src2.delete();
</script>
<script type="text/javascript">
let utils = new Utils('errorMessage');

utils.loadCode('codeSnippet', 'codeEditor');
utils.loadImageToCanvas('apple.jpg', 'canvasInput1');
utils.loadImageToCanvas('orange.jpg', 'canvasInput2');

let trackbar = document.getElementById('trackbar');
trackbar.addEventListener('input', () => {
    utils.executeCode('codeEditor');
});

let weightValue = document.getElementById('weightValue');
weightValue.innerText = trackbar.value;
trackbar.addEventListener('input', () => {
    weightValue.innerText = trackbar.value;
});

utils.loadOpenCv(() => {
    trackbar.removeAttribute('disabled');
    utils.executeCode('codeEditor');
});
</script>
</body>
</html>
