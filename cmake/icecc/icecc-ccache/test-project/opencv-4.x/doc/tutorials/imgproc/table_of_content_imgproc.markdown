Image Processing (imgproc module) {#tutorial_table_of_content_imgproc}
=================================

@tableofcontents

The imgproc module in OpenCV is a collection of per-pixel image operations (color conversions, filters) drawing (contours, objects, text), and
geometry transformations (warping, resize) useful for computer vision.
Here's an overview of the content in the imgproc module, categorized for easier navigation:

##### Basic
These tutorials cover fundamental image processing tasks, such as drawing on images, applying filters, and morphological operations.

-   @subpage tutorial_basic_geometric_drawing
-   @subpage tutorial_random_generator_and_text
-   @subpage tutorial_gausian_median_blur_bilateral_filter
-   @subpage tutorial_erosion_dilatation
-   @subpage tutorial_opening_closing_hats
-   @subpage tutorial_hitOrMiss
-   @subpage tutorial_morph_lines_detection
-   @subpage tutorial_pyramids
-   @subpage tutorial_threshold
-   @subpage tutorial_threshold_inRange

##### Transformations
These tutorials explore more advanced transformations that modify the image in various ways, such as filtering, warping, and edge detection.

-   @subpage tutorial_filter_2d
-   @subpage tutorial_copyMakeBorder
-   @subpage tutorial_sobel_derivatives
-   @subpage tutorial_laplace_operator
-   @subpage tutorial_canny_detector
-   @subpage tutorial_hough_lines
-   @subpage tutorial_hough_circle
-   @subpage tutorial_generalized_hough_ballard_guil
-   @subpage tutorial_remap
-   @subpage tutorial_warp_affine

##### Histograms
Histograms are vital for image analysis, and these tutorials cover operations like equalization, comparison, and back projection.

-   @subpage tutorial_histogram_equalization
-   @subpage tutorial_histogram_calculation
-   @subpage tutorial_histogram_comparison
-   @subpage tutorial_back_projection
-   @subpage tutorial_template_matching

##### Contours
Contours are curves that represent the boundaries of objects in an image. These tutorials cover techniques to detect and analyze contours.

-   @subpage tutorial_find_contours
-   @subpage tutorial_hull
-   @subpage tutorial_bounding_rects_circles
-   @subpage tutorial_bounding_rotated_ellipses
-   @subpage tutorial_moments
-   @subpage tutorial_point_polygon_test

##### Others
These tutorials cover specialized image processing techniques for more complex tasks like deblurring, noise removal, and image segmentation.

-   @subpage tutorial_distance_transform
-   @subpage tutorial_out_of_focus_deblur_filter
-   @subpage tutorial_motion_deblur_filter
-   @subpage tutorial_anisotropic_image_segmentation_by_a_gst
-   @subpage tutorial_periodic_noise_removing_filter
