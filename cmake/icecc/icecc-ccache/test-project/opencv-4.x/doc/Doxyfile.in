# Doxyfile 1.12.0

#---------------------------------------------------------------------------
# Project related configuration options
#---------------------------------------------------------------------------
DOXYFILE_ENCODING      = UTF-8
PROJECT_NAME           = OpenCV
PROJECT_NUMBER         = @OPENCV_VERSION@
PROJECT_BRIEF          = "Open Source Computer Vision"
PROJECT_LOGO           = @CMAKE_CURRENT_SOURCE_DIR@/opencv-logo-small.png
PROJECT_ICON           = @CMAKE_CURRENT_SOURCE_DIR@/opencv.ico
OUTPUT_DIRECTORY       = @CMAKE_DOXYGEN_OUTPUT_PATH@
CREATE_SUBDIRS         = YES
CREATE_SUBDIRS_LEVEL   = 8
ALLOW_UNICODE_NAMES    = NO
OUTPUT_LANGUAGE        = English
BRIEF_MEMBER_DESC      = YES
REPEAT_BRIEF           = YES
ABBREVIATE_BRIEF       = "The $name class" \
                         "The $name widget" \
                         "The $name file" \
                         is \
                         provides \
                         specifies \
                         contains \
                         represents \
                         a \
                         an \
                         the
ALWAYS_DETAILED_SEC    = NO
INLINE_INHERITED_MEMB  = NO
FULL_PATH_NAMES        = YES
STRIP_FROM_PATH        = @CMAKE_SOURCE_DIR@/modules \
                         @CMAKE_DOXYGEN_INCLUDE_ROOTS@
STRIP_FROM_INC_PATH    = @CMAKE_DOXYGEN_INCLUDE_ROOTS@
SHORT_NAMES            = NO
JAVADOC_AUTOBRIEF      = NO
JAVADOC_BANNER         = NO
QT_AUTOBRIEF           = NO
MULTILINE_CPP_IS_BRIEF = NO
PYTHON_DOCSTRING       = YES
INHERIT_DOCS           = YES
SEPARATE_MEMBER_PAGES  = NO
TAB_SIZE               = 4
ALIASES               += add_toggle{1}="@htmlonly[block] <div class='newInnerHTML'>\1</div><div> <script type='text/javascript'> addToggle(); </script>@endhtmlonly"
ALIASES               += add_toggle_cpp="@htmlonly[block] <div class='newInnerHTML' title='cpp' style='display: none;'>C++</div><div class='toggleable_div label_cpp' style='display: none;'>@endhtmlonly"
ALIASES               += add_toggle_java="@htmlonly[block] <div class='newInnerHTML' title='java' style='display: none;'>Java</div><div class='toggleable_div label_java' style='display: none;'>@endhtmlonly"
ALIASES               += add_toggle_python="@htmlonly[block] <div class='newInnerHTML' title='python' style='display: none;'>Python</div><div class='toggleable_div label_python' style='display: none;'>@endhtmlonly"
ALIASES               += end_toggle="@htmlonly[block] </div> @endhtmlonly"
ALIASES               += prev_tutorial{1}="**Prev  Tutorial:** \ref \1 \n"
ALIASES               += next_tutorial{1}="**Next  Tutorial:** \ref \1 \n"
ALIASES               += youtube{1}="@htmlonly[block]<div align='center'><iframe title='Video' width='560' height='349' src='https://www.youtube.com/embed/\1?rel=0' frameborder='0' align='middle' allowfullscreen></iframe></div>@endhtmlonly"
OPTIMIZE_OUTPUT_FOR_C  = NO
OPTIMIZE_OUTPUT_JAVA   = NO
OPTIMIZE_FOR_FORTRAN   = NO
OPTIMIZE_OUTPUT_VHDL   = NO
OPTIMIZE_OUTPUT_SLICE  = NO
EXTENSION_MAPPING      =
MARKDOWN_SUPPORT       = YES
TOC_INCLUDE_HEADINGS   = 5
MARKDOWN_ID_STYLE      = DOXYGEN
AUTOLINK_SUPPORT       = YES
BUILTIN_STL_SUPPORT    = YES
CPP_CLI_SUPPORT        = NO
SIP_SUPPORT            = NO
IDL_PROPERTY_SUPPORT   = YES
DISTRIBUTE_GROUP_DOC   = NO
GROUP_NESTED_COMPOUNDS = NO
SUBGROUPING            = YES
INLINE_GROUPED_CLASSES = NO
INLINE_SIMPLE_STRUCTS  = NO
TYPEDEF_HIDES_STRUCT   = YES
LOOKUP_CACHE_SIZE      = 0
NUM_PROC_THREADS       = 1
TIMESTAMP              = YES
#---------------------------------------------------------------------------
# Build related configuration options
#---------------------------------------------------------------------------
EXTRACT_ALL            = YES
EXTRACT_PRIVATE        = NO
EXTRACT_PRIV_VIRTUAL   = NO
EXTRACT_PACKAGE        = NO
EXTRACT_STATIC         = YES
EXTRACT_LOCAL_CLASSES  = NO
EXTRACT_LOCAL_METHODS  = NO
EXTRACT_ANON_NSPACES   = NO
RESOLVE_UNNAMED_PARAMS = YES
HIDE_UNDOC_MEMBERS     = NO
HIDE_UNDOC_CLASSES     = NO
HIDE_FRIEND_COMPOUNDS  = NO
HIDE_IN_BODY_DOCS      = NO
INTERNAL_DOCS          = NO
CASE_SENSE_NAMES       = YES
HIDE_SCOPE_NAMES       = NO
HIDE_COMPOUND_REFERENCE= NO
SHOW_HEADERFILE        = YES
SHOW_INCLUDE_FILES     = YES
SHOW_GROUPED_MEMB_INC  = YES
FORCE_LOCAL_INCLUDES   = NO
INLINE_INFO            = YES
SORT_MEMBER_DOCS       = YES
SORT_BRIEF_DOCS        = YES
SORT_MEMBERS_CTORS_1ST = YES
SORT_GROUP_NAMES       = NO
SORT_BY_SCOPE_NAME     = NO
STRICT_PROTO_MATCHING  = NO
GENERATE_TODOLIST      = YES
GENERATE_TESTLIST      = YES
GENERATE_BUGLIST       = YES
GENERATE_DEPRECATEDLIST= YES
ENABLED_SECTIONS       = @CMAKE_DOXYGEN_ENABLED_SECTIONS@
MAX_INITIALIZER_LINES  = 30
SHOW_USED_FILES        = YES
SHOW_FILES             = YES
SHOW_NAMESPACES        = YES
FILE_VERSION_FILTER    =
LAYOUT_FILE            = @CMAKE_DOXYGEN_LAYOUT@
CITE_BIB_FILES         = @CMAKE_EXTRA_BIB_FILES@
EXTERNAL_TOOL_PATH     =
#---------------------------------------------------------------------------
# Configuration options related to warning and progress messages
#---------------------------------------------------------------------------
QUIET                  = YES
WARNINGS               = YES
WARN_IF_UNDOCUMENTED   = YES
WARN_IF_DOC_ERROR      = YES
WARN_IF_INCOMPLETE_DOC = YES
WARN_NO_PARAMDOC       = NO
WARN_IF_UNDOC_ENUM_VAL = NO
WARN_AS_ERROR          = NO
WARN_FORMAT            = "$file:$line: $text"
WARN_LINE_FORMAT       = "at line $line of file $file"
WARN_LOGFILE           =
#---------------------------------------------------------------------------
# Configuration options related to the input files
#---------------------------------------------------------------------------
INPUT                  = @CMAKE_DOXYGEN_INPUT_LIST@
INPUT_ENCODING         = UTF-8
INPUT_FILE_ENCODING    =
FILE_PATTERNS          =
RECURSIVE              = YES
EXCLUDE                = @CMAKE_DOXYGEN_EXCLUDE_LIST@
EXCLUDE_SYMLINKS       = NO
EXCLUDE_PATTERNS       = *.inl.hpp *.impl.hpp *_detail.hpp */cudev/**/detail/*.hpp *.m */opencl/runtime/* */legacy/* *_c.h @DOXYGEN_EXCLUDE_PATTERNS@
EXCLUDE_SYMBOLS        = cv::DataType<*> cv::traits::* int void CV__* T __CV* cv::gapi::detail*
EXAMPLE_PATH           = @CMAKE_DOXYGEN_EXAMPLE_PATH@
EXAMPLE_PATTERNS       = *
EXAMPLE_RECURSIVE      = YES
IMAGE_PATH             = @CMAKE_DOXYGEN_IMAGE_PATH@
INPUT_FILTER           =
FILTER_PATTERNS        =
FILTER_SOURCE_FILES    = NO
FILTER_SOURCE_PATTERNS =
USE_MDFILE_AS_MAINPAGE =
FORTRAN_COMMENT_AFTER  = 72
#---------------------------------------------------------------------------
# Configuration options related to source browsing
#---------------------------------------------------------------------------
SOURCE_BROWSER         = NO
INLINE_SOURCES         = NO
STRIP_CODE_COMMENTS    = YES
REFERENCED_BY_RELATION = NO
REFERENCES_RELATION    = NO
REFERENCES_LINK_SOURCE = YES
SOURCE_TOOLTIPS        = YES
USE_HTAGS              = NO
VERBATIM_HEADERS       = NO
CLANG_ASSISTED_PARSING = NO
CLANG_ADD_INC_PATHS    = YES
CLANG_OPTIONS          =
CLANG_DATABASE_PATH    =
#---------------------------------------------------------------------------
# Configuration options related to the alphabetical class index
#---------------------------------------------------------------------------
ALPHABETICAL_INDEX     = YES
IGNORE_PREFIX          =
#---------------------------------------------------------------------------
# Configuration options related to the HTML output
#---------------------------------------------------------------------------
GENERATE_HTML          = YES
HTML_OUTPUT            = html
HTML_FILE_EXTENSION    = .html
HTML_HEADER            = @CMAKE_CURRENT_SOURCE_DIR@/header.html
HTML_FOOTER            = @CMAKE_CURRENT_SOURCE_DIR@/footer.html
HTML_STYLESHEET        =
HTML_EXTRA_STYLESHEET  = @CMAKE_CURRENT_SOURCE_DIR@/stylesheet.css
HTML_EXTRA_FILES       = @CMAKE_DOXYGEN_HTML_FILES@
HTML_COLORSTYLE        = LIGHT
HTML_COLORSTYLE_HUE    = 220
HTML_COLORSTYLE_SAT    = 100
HTML_COLORSTYLE_GAMMA  = 80
HTML_DYNAMIC_MENUS     = YES
HTML_DYNAMIC_SECTIONS  = NO
HTML_CODE_FOLDING      = YES
HTML_COPY_CLIPBOARD    = YES
HTML_PROJECT_COOKIE    =
HTML_INDEX_NUM_ENTRIES = 100
GENERATE_DOCSET        = NO
DOCSET_FEEDNAME        = "Doxygen generated docs"
DOCSET_FEEDURL         =
DOCSET_BUNDLE_ID       = org.doxygen.Project
DOCSET_PUBLISHER_ID    = org.doxygen.Publisher
DOCSET_PUBLISHER_NAME  = Publisher
GENERATE_HTMLHELP      = NO
CHM_FILE               =
HHC_LOCATION           =
GENERATE_CHI           = NO
CHM_INDEX_ENCODING     =
BINARY_TOC             = NO
TOC_EXPAND             = NO
SITEMAP_URL            =
GENERATE_QHP           = @CMAKE_DOXYGEN_GENERATE_QHP@
QCH_FILE               = ../opencv-@OPENCV_VERSION@.qch
QHP_NAMESPACE          = org.opencv.@OPENCV_VERSION@
QHP_VIRTUAL_FOLDER     = opencv
QHP_CUST_FILTER_NAME   =
QHP_CUST_FILTER_ATTRS  =
QHP_SECT_FILTER_ATTRS  =
QHG_LOCATION           =
GENERATE_ECLIPSEHELP   = NO
ECLIPSE_DOC_ID         = org.doxygen.Project
DISABLE_INDEX          = NO
GENERATE_TREEVIEW      = NO
FULL_SIDEBAR           = NO
ENUM_VALUES_PER_LINE   = 1
SHOW_ENUM_VALUES       = NO
TREEVIEW_WIDTH         = 250
EXT_LINKS_IN_WINDOW    = YES
OBFUSCATE_EMAILS       = YES
HTML_FORMULA_FORMAT    = svg
FORMULA_FONTSIZE       = 14
FORMULA_MACROFILE      =
USE_MATHJAX            = YES
MATHJAX_VERSION        = MathJax_3
MATHJAX_FORMAT         = chtml
MATHJAX_RELPATH        = @OPENCV_MATHJAX_RELPATH@
MATHJAX_EXTENSIONS     = ams
MATHJAX_CODEFILE       = @CMAKE_CURRENT_SOURCE_DIR@/mymath.js
SEARCHENGINE           = YES
SERVER_BASED_SEARCH    = NO
EXTERNAL_SEARCH        = NO
SEARCHENGINE_URL       =
SEARCHDATA_FILE        = searchdata.xml
EXTERNAL_SEARCH_ID     =
EXTRA_SEARCH_MAPPINGS  =
#---------------------------------------------------------------------------
# Configuration options related to the LaTeX output
#---------------------------------------------------------------------------
GENERATE_LATEX         = NO
LATEX_OUTPUT           = latex
LATEX_CMD_NAME         = latex
MAKEINDEX_CMD_NAME     = makeindex
LATEX_MAKEINDEX_CMD    = makeindex
COMPACT_LATEX          = NO
PAPER_TYPE             = a4
EXTRA_PACKAGES         = mymath
LATEX_HEADER           =
LATEX_FOOTER           =
LATEX_EXTRA_STYLESHEET =
LATEX_EXTRA_FILES      =
PDF_HYPERLINKS         = YES
USE_PDFLATEX           = YES
LATEX_BATCHMODE        = NO
LATEX_HIDE_INDICES     = NO
LATEX_BIB_STYLE        = plain
LATEX_EMOJI_DIRECTORY  =
#---------------------------------------------------------------------------
# Configuration options related to the RTF output
#---------------------------------------------------------------------------
GENERATE_RTF           = NO
RTF_OUTPUT             = rtf
COMPACT_RTF            = NO
RTF_HYPERLINKS         = NO
RTF_STYLESHEET_FILE    =
RTF_EXTENSIONS_FILE    =
RTF_EXTRA_FILES        =
#---------------------------------------------------------------------------
# Configuration options related to the man page output
#---------------------------------------------------------------------------
GENERATE_MAN           = NO
MAN_OUTPUT             = man
MAN_EXTENSION          = .3
MAN_SUBDIR             =
MAN_LINKS              = NO
#---------------------------------------------------------------------------
# Configuration options related to the XML output
#---------------------------------------------------------------------------
GENERATE_XML           = NO
XML_OUTPUT             = xml
XML_PROGRAMLISTING     = YES
XML_NS_MEMB_FILE_SCOPE = NO
#---------------------------------------------------------------------------
# Configuration options related to the DOCBOOK output
#---------------------------------------------------------------------------
GENERATE_DOCBOOK       = NO
DOCBOOK_OUTPUT         = docbook
#---------------------------------------------------------------------------
# Configuration options for the AutoGen Definitions output
#---------------------------------------------------------------------------
GENERATE_AUTOGEN_DEF   = NO
#---------------------------------------------------------------------------
# Configuration options related to Sqlite3 output
#---------------------------------------------------------------------------
GENERATE_SQLITE3       = NO
SQLITE3_OUTPUT         = sqlite3
SQLITE3_RECREATE_DB    = YES
#---------------------------------------------------------------------------
# Configuration options related to the Perl module output
#---------------------------------------------------------------------------
GENERATE_PERLMOD       = NO
PERLMOD_LATEX          = NO
PERLMOD_PRETTY         = YES
PERLMOD_MAKEVAR_PREFIX =
#---------------------------------------------------------------------------
# Configuration options related to the preprocessor
#---------------------------------------------------------------------------
ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = NO
SEARCH_INCLUDES        = YES
INCLUDE_PATH           =
INCLUDE_FILE_PATTERNS  =
PREDEFINED             = __cplusplus=1 \
                         CVAPI(x)=x \
                         CV_API_CALL= \
                         CV_DOXYGEN= \
                         CV_EXPORTS= \
                         CV_EXPORTS_W= \
                         CV_EXPORTS_W_SIMPLE= \
                         CV_EXPORTS_AS(x)= \
                         CV_EXPORTS_W_MAP= \
                         GAPI_EXPORTS= \
                         GAPI_EXPORTS_W= \
                         CV_IN_OUT= \
                         CV_OUT= \
                         CV_PROP= \
                         CV_PROP_RW= \
                         CV_WRAP= \
                         CV_WRAP_AS(x)= \
                         CV_WRAP_MAPPABLE(x)= \
                         CV_WRAP_PHANTOM(x)= \
                         CV_WRAP_DEFAULT(x)= \
                         CV_CDECL= \
                         CV_Func= \
                         CV_DO_PRAGMA(x)= \
                         CV_SUPPRESS_DEPRECATED_START= \
                         CV_SUPPRESS_DEPRECATED_END= \
                         CV_INLINE= \
                         CV_NORETURN= \
                         CV_DEFAULT(x)=" = x" \
                         CV_NEON=1 \
                         CV_SSE2=1 \
                         CV_SIMD128=1 \
                         CV_SIMD256=1 \
                         CV_SIMD512=1 \
                         CV_SIMD128_64F=1 \
                         CV_SIMD256_64F=1 \
                         CV_SIMD512_64F=1 \
                         CV__DEBUG_NS_BEGIN= \
                         CV__DEBUG_NS_END= \
                         CV_DEPRECATED_EXTERNAL= \
                         CV_DEPRECATED=
EXPAND_AS_DEFINED      =
SKIP_FUNCTION_MACROS   = YES
#---------------------------------------------------------------------------
# Configuration options related to external references
#---------------------------------------------------------------------------
TAGFILES               =
GENERATE_TAGFILE       = @CMAKE_DOXYGEN_OUTPUT_PATH@/html/opencv.tag
ALLEXTERNALS           = NO
EXTERNAL_GROUPS        = YES
EXTERNAL_PAGES         = YES
#---------------------------------------------------------------------------
# Configuration options related to diagram generator tools
#---------------------------------------------------------------------------
HIDE_UNDOC_RELATIONS   = NO
HAVE_DOT               = @CMAKECONFIG_HAVE_DOT@
DOT_NUM_THREADS        = 0
DOT_COMMON_ATTR        = "fontname=Helvetica,fontsize=10"
DOT_EDGE_ATTR          = "labelfontname=Helvetica,labelfontsize=10"
DOT_NODE_ATTR          = "shape=box,height=0.2,width=0.4"
DOT_FONTPATH           =
CLASS_GRAPH            = NO
COLLABORATION_GRAPH    = YES
GROUP_GRAPHS           = NO
UML_LOOK               = YES
UML_LIMIT_NUM_FIELDS   = 10
DOT_UML_DETAILS        = NO
DOT_WRAP_THRESHOLD     = 17
TEMPLATE_RELATIONS     = YES
INCLUDE_GRAPH          = YES
INCLUDED_BY_GRAPH      = YES
CALL_GRAPH             = YES
CALLER_GRAPH           = NO
GRAPHICAL_HIERARCHY    = YES
DIRECTORY_GRAPH        = YES
DIR_GRAPH_MAX_DEPTH    = 1
DOT_IMAGE_FORMAT       = @CMAKECONFIG_DOT_IMAGE_FORMAT@
INTERACTIVE_SVG        = @CMAKECONFIG_INTERACTIVE_SVG@
DOT_PATH               = @CMAKECONFIG_DOT_PATH@
DOTFILE_DIRS           =
DIA_PATH               =
DIAFILE_DIRS           =
PLANTUML_JAR_PATH      =
PLANTUML_CFG_FILE      =
PLANTUML_INCLUDE_PATH  =
DOT_GRAPH_MAX_NODES    = 250
MAX_DOT_GRAPH_DEPTH    = 0
DOT_MULTI_TARGETS      = NO
GENERATE_LEGEND        = YES
DOT_CLEANUP            = YES
MSCGEN_TOOL            =
MSCFILE_DIRS           =
