# Icecream 分布式编译解决方案

## 项目概述

这是一个完整的icecream分布式编译解决方案，专为C/C++项目的高效构建而设计。该解决方案包含了从基础镜像构建到生产环境部署的完整工具链，支持CI/CD集成和多种部署模式。

版本：
 - icecc 1.4.90
 - icecream-sundae 1.1.0(修改了icecream-sundae/src/scheduler.cpp文件以适配新版icecc)

### 核心特性

- 🚀 **分布式编译**: 基于icecream的分布式C/C++编译加速
- 📦 **编译缓存**: 集成ccache，提高重复编译效率
- 🔧 **多版本支持**: 支持多种CMake和GCC版本组合
- 🏗️ **CI/CD集成**: 完整的Jenkins流水线支持
- 🐳 **容器化部署**: 基于Docker的一键部署方案
- ⚡ **性能优化**: 针对大型项目的编译性能优化

## 目录结构

```
cmake/icecc/
├── README.md                          # 本文档 - 完整解决方案指南
├── icecream/                          # icecream基础镜像构建
│   ├── Dockerfile                     # icecream核心服务镜像构建文件
│   ├── icecream-scheduler.yml         # 调度器服务Docker Compose配置
│   └── icecream-worker.yml           # 工作节点服务Docker Compose配置
├── icecc-ccache/                      # 集成构建镜像（cmake + ccache + icecream）
│   ├── DISTRIBUTED-DEPLOYMENT.md     # 分布式部署详细指南
│   ├── ccache-wrapper.sh             # ccache路径统一包装脚本
│   ├── cmake2.8.12-gcc4.8.5/        # CMake 2.8.12 + GCC 4.8.5 构建镜像
│   ├── cmake2.8.12-gcc5.5.0/        # CMake 2.8.12 + GCC 5.5.0 构建镜像
│   ├── cmake2.8.12-gcc6.4.0/        # CMake 2.8.12 + GCC 6.4.0 构建镜像
│   ├── cmake3.10.1-gcc5.5.0/        # CMake 3.10.1 + GCC 5.5.0 构建镜像
│   ├── deploy-scheduler.sh           # 调度器一键部署脚本
│   ├── deploy-worker.sh              # 工作节点一键部署脚本
│   ├── deploy-client.sh              # 客户端构建脚本
│   ├── distributed-build-example.sh  # 分布式构建示例脚本
│   ├── docker-compose-demo.yml       # 单机演示Docker Compose配置
│   └── env-config-example.sh         # 环境变量配置示例
└── jenkins/                           # Jenkins CI/CD集成
    ├── JENKINS-CONFIGURATION.md      # Jenkins配置详细指南
    ├── Jenkinsfile.basic             # 基础Jenkins流水线
    ├── Jenkinsfile.advanced          # 高级Jenkins流水线（参数化）
    ├── Jenkinsfile.flexible          # 灵活Jenkins流水线（多环境）
    └── vars/
        └── icecreamBuild.groovy      # Jenkins共享库函数
```

## 核心组件详解

### 1. icecream/ - 基础镜像构建

**作用**: 构建icecream分布式编译的核心服务镜像

- **Dockerfile**:
  - 基于CentOS 7构建icecream核心服务
  - 包含调度器(icecc-scheduler)和工作节点(iceccd)
  - 支持多种运行模式：调度器、工作节点、客户端
  - 镜像标签：`icecream-centos7:20250930`

- **icecream-scheduler.yml**:
  - Docker Compose配置文件
  - 用于快速启动调度器服务
  - 暴露端口：10245(管理), 8765(通信), 8766(备用)

- **icecream-worker.yml**:
  - Docker Compose配置文件
  - 用于快速启动工作节点服务
  - 自动连接到指定的调度器

### 2. icecc-ccache/ - 集成构建镜像

**作用**: 整合cmake、ccache、icecream的完整构建环境

#### 构建镜像版本
- **cmake2.8.12-gcc4.8.5/**: CMake 2.8.12 + GCC 4.8.5（系统默认）
- **cmake2.8.12-gcc5.5.0/**: CMake 2.8.12 + GCC 5.5.0（编译安装）
- **cmake2.8.12-gcc6.4.0/**: CMake 2.8.12 + GCC 6.4.0（编译安装）
- **cmake3.10.1-gcc5.5.0/**: CMake 3.10.1 + GCC 5.5.0（编译安装）

#### 核心脚本和配置
- **ccache-wrapper.sh**: 路径统一包装脚本，解决跨容器路径问题
- **env-config-example.sh**: 完整的环境变量配置示例
- **DISTRIBUTED-DEPLOYMENT.md**: 详细的分布式部署指南

#### 部署脚本
- **deploy-scheduler.sh**: 一键部署调度器节点
- **deploy-worker.sh**: 一键部署工作节点
- **deploy-client.sh**: 客户端构建脚本，支持交互式和自动化构建
- **distributed-build-example.sh**: 完整的分布式构建演示

#### 配置文件
- **docker-compose-demo.yml**: 单机演示环境，包含1个调度器+2个工作节点

### 3. jenkins/ - CI/CD集成

**作用**: 提供完整的Jenkins流水线集成支持

- **JENKINS-CONFIGURATION.md**:
  - Jenkins环境配置详细指南
  - 包含节点配置、插件安装、权限设置等

- **Jenkinsfile.basic**:
  - 基础Jenkins流水线
  - 适用于固定调度器地址的简单场景
  - 包含环境检查、CMake配置、分布式编译等阶段

- **Jenkinsfile.advanced**:
  - 高级参数化流水线
  - 支持动态镜像选择、构建类型选择
  - 包含强制本地编译选项

- **Jenkinsfile.flexible**:
  - 灵活多环境流水线
  - 支持多环境部署和测试

- **vars/icecreamBuild.groovy**:
  - Jenkins共享库函数
  - 封装常用的icecream构建逻辑
  - 支持简化调用和高级配置

## 快速开始

### 1. 构建基础镜像

```bash
# 构建icecream基础镜像
cd cmake/icecc/icecream
docker build -t icecream-centos7:20250930 .
```

### 2. 构建集成镜像

```bash
# 选择需要的版本构建
cd cmake/icecc/icecc-ccache/cmake2.8.12-gcc4.8.5
docker build -t cmake-ccache-icecream:cmake2.8.12-gcc4.8.5 .

# 或构建其他版本
cd ../cmake3.10.1-gcc5.5.0
docker build -t cmake-ccache-icecream:cmake3.10.1-gcc5.5.0 .
```

### 3. 部署分布式环境

#### 方式1: 单机演示（推荐入门）

```bash
cd cmake/icecc/icecc-ccache
docker-compose -f docker-compose-demo.yml up -d
```

#### 方式2: 真实分布式部署

```bash
# 在调度器机器上
./deploy-scheduler.sh

# 在工作节点机器上
./deploy-worker.sh ************* worker-node-1

# 在客户端机器上进行构建
./deploy-client.sh ************* /path/to/project --build
```

### 分布式编译

> **重要说明**：分布式编译有两种部署方式，请根据使用场景选择：

#### 方式1: 单机演示环境（用于功能验证和学习）

**适用场景**：功能测试、学习icecream、CI/CD流水线验证

```bash
# 启动单机演示环境（所有组件在同一台机器上）
docker-compose -f docker-compose-demo.yml up -d

# 进入构建客户端进行编译
docker-compose -f docker-compose-demo.yml exec build-client bash

# 在客户端容器内使用分布式编译
cmake .
make -j8  # 使用本机的多个"分布式"节点

# 停止演示环境
docker-compose -f docker-compose-demo.yml down
```

**注意**：此方式所有容器运行在同一台机器上，主要用于功能验证，无法发挥真正的分布式性能优势。

#### 方式2: 真实分布式环境（生产推荐）

**适用场景**：生产环境、大型项目构建、需要真正分布式性能提升的场景

**快速部署**：
```bash
# 1. 在调度器机器上（如：*************）
./deploy-scheduler.sh

# 2. 在工作节点机器上（如：192.168.1.101, 192.168.1.102）
./deploy-worker.sh ************* worker-node-1
./deploy-worker.sh ************* worker-node-2

# 3. 在客户端机器上进行编译
./deploy-client.sh ************* /path/to/project --interactive
```

**详细部署指南**：参见 [DISTRIBUTED-DEPLOYMENT.md](icecc-ccache/DISTRIBUTED-DEPLOYMENT.md)

#### 方式3: 运行完整示例
```bash
# 单机演示示例
./distributed-build-example.sh

# 真实分布式示例（需要多台机器）
# 参见 DISTRIBUTED-DEPLOYMENT.md 中的详细步骤
```

## 使用场景详解

### 场景1: 本地开发环境

```bash
# 使用集成镜像进行本地构建
docker run --rm -v $(pwd):/workspace -w /workspace \
  cmake-ccache-icecream:cmake2.8.12-gcc4.8.5 \
  bash -c "cmake . && make -j$(nproc)"
```

### 场景2: CI/CD流水线

#### 默认模式（ccache + icecream集成）
推荐用于生产环境，同时享受缓存和分布式编译的好处：

```bash
# Jenkins Pipeline中使用
docker run --rm \
  -v ${WORKSPACE}:/workspace -w /workspace \
  -v ${JENKINS_HOME}/.ccache:/root/.ccache \
  -e ICECC_SCHEDULER="*************:8765" \
  cmake-ccache-icecream:cmake2.8.12-gcc4.8.5 \
  bash -c "cmake . && make -j8"
```

#### 强制icecream模式（调试用）
适用于调试分布式编译问题：

```bash
# 强制使用icecream（绕过ccache）
docker run --rm -v $(pwd):/workspace -w /workspace \
  -e CC="icecc gcc" \
  -e CXX="icecc g++" \
  -e CCACHE_PREFIX="" \
  -e ICECC_SCHEDULER="*************:8765" \
  cmake-ccache-icecream:cmake2.8.12-gcc4.8.5 \
  bash -c "cmake . && make -j8"
```

### 场景3: 大型项目分布式构建

```bash
# 使用部署脚本进行真实分布式构建
cd cmake/icecc/icecc-ccache

# 1. 部署调度器（在专门的调度器机器上）
./deploy-scheduler.sh

# 2. 部署工作节点（在多台工作机器上）
./deploy-worker.sh ************* worker-gpu-01
./deploy-worker.sh ************* worker-cpu-02

# 3. 客户端构建（在开发机器上）
./deploy-client.sh ************* /path/to/large-project --build
```

### 两种编译模式对比

| 特性 | 默认模式 (CCACHE_PREFIX="icecc") | 强制模式 (CC="icecc gcc") |
|------|----------------------------------|---------------------------|
| **缓存支持** | ✅ 支持ccache缓存 | ❌ 绕过ccache |
| **分布式编译** | ✅ 通过ccache调用icecc | ✅ 直接使用icecc |
| **适用场景** | CI/CD生产环境 | 调试和测试 |
| **性能** | 缓存命中时更快 | 始终分布式编译 |
| **网络使用** | 智能优化 | 所有编译都走网络 |
| **推荐用途** | 日常开发和CI | 性能测试和故障排除 |

## Jenkins集成指南

### 基础集成

使用提供的Jenkinsfile模板：

```groovy
// 在Jenkinsfile中使用
@Library('icecream-build-lib') _

pipeline {
    agent {
        docker {
            image 'cmake-ccache-icecream:cmake2.8.12-gcc4.8.5'
            args '-v ${JENKINS_HOME}/.ccache:/root/.ccache --network host'
        }
    }

    environment {
        ICECC_SCHEDULER = '*************:8765'
        CCACHE_PREFIX = 'icecc'
    }

    stages {
        stage('Build') {
            steps {
                sh 'cmake . && make -j8'
            }
        }
    }
}
```

### 高级集成

使用共享库函数：

```groovy
@Library('icecream-build-lib') _

// 简单使用
icecreamBuild([
    scheduler: '*************:8765',
    image: 'cmake-ccache-icecream:cmake3.10.1-gcc5.5.0'
])

// 高级配置
icecreamBuild([
    scheduler: '*************:8765',
    image: 'cmake-ccache-icecream:cmake2.8.12-gcc6.4.0',
    buildType: 'Release',
    ccacheSize: '10G',
    enableStats: true
])
```

详细配置请参考：[jenkins/JENKINS-CONFIGURATION.md](jenkins/JENKINS-CONFIGURATION.md)

## 环境变量配置

### 核心环境变量

#### ccache配置
- `CCACHE_DIR=/root/.ccache` - ccache缓存目录
- `CCACHE_MAXSIZE=5G` - 最大缓存大小
- `CCACHE_COMPRESS=1` - 启用压缩
- `CCACHE_BASEDIR=/build-workspace` - 基础目录
- `CCACHE_STATS=1` - 启用统计
- `CCACHE_PREFIX=icecc` - **关键配置**：让ccache调用icecc进行分布式编译

#### icecream配置
- `ICECC_VERSION=/opt/icecc-envs/gcc-env.tar.gz` - **关键配置**：编译环境包路径
- `ICECC_SCHEDULER=调度器IP:8765` - 调度器地址（客户端使用）
- `ICECC_DEBUG=info` - 调试级别（可选）

#### PATH配置
优化后的PATH配置（配合CCACHE_PREFIX使用）：
```
/usr/lib64/ccache:/opt/icecream/bin:/opt/icecream/sbin:/usr/local/bin:/usr/bin
```

**重要说明**：
- 使用`CCACHE_PREFIX=icecc`方式时，PATH中不包含`/opt/icecream/libexec/icecc/bin`
- 这样避免ccache和icecc的包装器相互干扰
- ccache会通过CCACHE_PREFIX自动调用icecc

完整的环境变量配置示例请参考：[icecc-ccache/env-config-example.sh](icecc-ccache/env-config-example.sh)

## 性能优化和最佳实践

### 硬件配置建议

**调度器节点**：
- CPU: 2-4核心即可
- 内存: 2-4GB
- 网络: 良好的网络连接

**工作节点**：
- CPU: 尽可能多的核心
- 内存: 每个核心建议2-4GB
- 存储: SSD优先，足够的临时空间

**客户端**：
- 网络: 与调度器的低延迟连接
- 存储: 项目源码和构建产物

### 性能调优

```bash
# 工作节点资源限制
docker update --cpus=8 --memory=16g icecc-worker-node-1

# 调度器性能参数
docker run -d --name icecc-scheduler \
  -e ICECC_MAX_JOBS=100 \
  -e ICECC_NICE=5 \
  icecream-centos7:20250930 \
  icecc-scheduler -u nobody -v

# 客户端编译参数
export ICECC_JOBS=8
make -j${ICECC_JOBS}
```

### 监控和调试

```bash

# 查看详细的编译分布情况
ICECC_DEBUG=1 make -j8

# 查看调度器日志
docker logs -f icecc-scheduler

# 查看工作节点日志
docker logs -f icecc-worker-node-1

# 查看ccache统计
ccache -s
```

## 故障排除

### 常见问题

#### 1. ccache不工作
```bash
# 检查ccache状态
ccache -s

# 清理ccache缓存
ccache -C

# 检查ccache配置
cat /root/.ccache/ccache.conf

# 验证ccache路径
which gcc  # 应该指向 /usr/lib64/ccache/gcc
```

#### 2. icecream连接问题
```bash
# 检查icecream状态
icecc --version

# 检查网络连接
ping *************
telnet ************* 8765

# 检查防火墙
sudo firewall-cmd --list-ports
sudo iptables -L

# 检查端口是否开放
netstat -tlnp | grep -E '(8765|10245|8766)'
```

#### 3. 分布式编译不生效
```bash
# 确认环境变量设置
echo $ICECC_SCHEDULER
echo $ICECC_VERSION
echo $CCACHE_PREFIX
echo $PATH

# 检查编译器包装器
which gcc
which g++

# 强制使用icecream（调试用）
export CC="icecc gcc"
export CXX="icecc g++"
export CCACHE_PREFIX=""
```

#### 4. 编译环境包问题
```bash
# 检查编译环境包
ls -la /opt/icecc-envs/
file /opt/icecc-envs/gcc-env.tar.gz

# 重新创建编译环境包
icecc-create-env gcc g++
```

### 调试技巧

```bash
# 启用详细调试输出
export ICECC_DEBUG=debug
export CCACHE_DEBUG=1

# 单线程编译以便观察问题
make -j1 VERBOSE=1

# 检查编译命令
make -n  # 显示将要执行的命令但不实际执行
```

## 注意事项

1. **基础镜像依赖**: 确保基础镜像 `icecream-centos7:20250930` 可用
2. **网络要求**: 分布式编译需要稳定的网络连接到icecream调度器
3. **缓存持久化**: ccache缓存目录建议挂载到宿主机以持久化缓存
4. **资源限制**: CI环境中建议设置适当的容器资源限制
5. **版本一致性**: 确保所有节点使用相同版本的镜像和编译环境包
6. **安全考虑**: 生产环境中建议配置防火墙和访问控制

## 相关文档

- [分布式部署详细指南](icecc-ccache/DISTRIBUTED-DEPLOYMENT.md)
- [Jenkins配置指南](jenkins/JENKINS-CONFIGURATION.md)
- [环境变量配置示例](icecc-ccache/env-config-example.sh)

## 贡献和支持

如有问题或建议，请参考相关文档或联系维护团队。
