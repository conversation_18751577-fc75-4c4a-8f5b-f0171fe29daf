FROM alpine:3.18
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
RUN apk update
RUN apk add git curl

# 只克隆icecream源码，icecream-sundae使用本地修改版本
RUN git clone --depth=1 --progress https://github.com/icecc/icecream.git


FROM centos:7 AS builder

# 配置CentOS镜像源
RUN mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak || true

RUN echo -e '[base]\n\
name=CentOS-7.9.2009 - Base\n\
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos-vault/7.9.2009/os/$basearch/\n\
gpgcheck=1\n\
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7\n\n\
[updates]\n\
name=CentOS-7.9.2009 - Updates\n\
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos-vault/7.9.2009/updates/$basearch/\n\
gpgcheck=1\n\
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7\n\n\
[extras]\n\
name=CentOS-7.9.2009 - Extras\n\
baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos-vault/7.9.2009/extras/$basearch/\n\
gpgcheck=1\n\
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7' > /etc/yum.repos.d/CentOS-Base.repo

RUN yum clean all

# 配置 yum 网络优化参数
RUN echo -e 'timeout=60\nretries=5\nkeepcache=0' >> /etc/yum.conf

# 安装 EPEL 仓库
RUN yum install -y epel-release

RUN yum update -y && yum install -y ca-certificates

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装构建 icecream 必需的核心开发工具
RUN yum install -y autoconf automake libtool make gcc gcc-c++ pkgconfig

# 安装 centos-release-scl 并配置正确的镜像源
RUN yum install -y centos-release-scl

# 配置 CentOS-SCLo-scl.repo
RUN echo -e '[centos-sclo-sclo]\n\
name=CentOS-7 - SCLo sclo\n\
baseurl=https://mirrors.aliyun.com/centos/7/sclo/x86_64/sclo/\n\
gpgcheck=0\n\
enabled=1\n\
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo' > /etc/yum.repos.d/CentOS-SCLo-scl.repo

# 配置 CentOS-SCLo-scl-rh.repo
RUN echo -e '[centos-sclo-rh]\n\
name=CentOS-7 - SCLo rh\n\
baseurl=https://mirrors.aliyun.com/centos/7/sclo/x86_64/rh/\n\
gpgcheck=0\n\
enabled=1\n\
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-SIG-SCLo' > /etc/yum.repos.d/CentOS-SCLo-scl-rh.repo

# 刷新缓存并安装 devtoolset-8
RUN yum clean all && \
    yum makecache && \
    yum install -y devtoolset-8-gcc*

# 注意：devtoolset-8 只在构建时临时使用，不永久替换系统编译器

RUN yum install -y libcap-ng-devel lzo-devel libzstd-devel libarchive-devel asciidoc clang \
tini openssl-devel cmake zip \
python3 python3-pip python3-devel wget ninja-build

RUN yum install -y glib2-devel icecc-devel ncurses-devel meson which

COPY --from=0 /icecream /icecream
# 复制本地修改过的icecream-sundae源码
COPY icecream-sundae /icecream-sundae

WORKDIR /icecream

# 编译安装 autoconf 2.71 以满足 icecream 构建要求
RUN echo "=== 编译安装 autoconf 2.71 ===" && \
    cd /tmp && \
    wget https://ftp.gnu.org/gnu/autoconf/autoconf-2.71.tar.xz && \
    tar -xf autoconf-2.71.tar.xz && \
    cd autoconf-2.71 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && \
    make install && \
    cd / && \
    rm -rf /tmp/autoconf-2.71* && \
    echo "=== autoconf 2.71 安装完成 ==="

# 更新 PATH 以优先使用新版本的 autoconf
ENV PATH=/usr/local/bin:$PATH

# 使用 devtoolset-8 临时激活 GCC 8 构建 icecream
RUN echo "临时激活 devtoolset-8 编译 icecream" && \
    source /opt/rh/devtoolset-8/enable && \
    ./autogen.sh && \
    ./configure --prefix=/opt/icecream CXXFLAGS="-std=c++14" && \
    make -j `grep "processor" /proc/cpuinfo | sort -u | wc -l` && \
    make install
ENV PATH=/opt/icecream/libexec/icecc/bin:/opt/icecream/bin:/opt/icecream/sbin:$PATH
EXPOSE 10245 8765 8766

# 验证最终镜像使用原始编译器版本
RUN echo "=== 验证最终编译器版本 ===" && \
    gcc --version && \
    echo "✓ 最终镜像使用原始 GCC 版本"

# 构建 icecream-sundae (使用本地修改过的源码)
WORKDIR /icecream-sundae

# 构建icecream-sundae
RUN echo "=== 构建 icecream-sundae ===" && \
    source /opt/rh/devtoolset-8/enable && \
    export CC=/opt/rh/devtoolset-8/root/bin/gcc && \
    export CXX=/opt/rh/devtoolset-8/root/bin/g++ && \
    export PKG_CONFIG_PATH=/opt/icecream/lib/pkgconfig:$PKG_CONFIG_PATH && \
    echo "验证编译器:" && \
    $CC --version && \
    $CXX --version && \
    echo "验证 icecc 依赖:" && \
    pkg-config --exists icecc && echo "✓ icecc 依赖找到" || echo "⚠ icecc 依赖未找到" && \
    echo "开始构建..." && \
    mkdir -p builddir && cd builddir && \
    if CC=$CC CXX=$CXX meson .. --buildtype release; then \
        echo "Meson配置成功，开始编译..."; \
        if ninja; then \
            echo "编译成功，安装icecream-sundae..."; \
            ninja install && echo "✓ icecream-sundae安装成功"; \
        else \
            echo "⚠ icecream-sundae编译失败，查看详细错误:"; \
            ninja -v || true; \
        fi; \
    else \
        echo "⚠ icecream-sundae Meson配置失败，查看配置日志:"; \
        cat meson-logs/meson-log.txt 2>/dev/null || echo "无配置日志"; \
    fi

WORKDIR /
RUN yum clean all && rm -rf /icecream /icecream-sundae
ENTRYPOINT ["tini", "--"]