FROM centos:7

# 添加构建参数，用于配置镜像源
ARG CENTOS_MIRROR=mirrors.aliyun.com

# 设置环境变量
ENV SDKMAN_DIR="/root/.sdkman"
ENV PATH="${SDKMAN_DIR}/bin:${SDKMAN_DIR}/candidates/ant/current/bin:${SDKMAN_DIR}/candidates/java/current/bin:${PATH}"
ENV JAVA_HOME="${SDKMAN_DIR}/candidates/java/current"
ENV ANT_HOME="${SDKMAN_DIR}/candidates/ant/current"

# 配置CentOS镜像源
RUN sed -e "s|^mirrorlist=|#mirrorlist=|g" \
    -e "s|^#baseurl=http://mirror.centos.org|baseurl=https://${CENTOS_MIRROR}|g" \
    -i.bak /etc/yum.repos.d/CentOS-*.repo

# 安装基础依赖
RUN yum -y update && \
    yum -y install curl wget zip unzip git which tar gzip \
    && yum clean all

RUN set -eux; \
    # 安装SDKMAN
    curl -s "https://get.sdkman.io?ci=true&rcupdate=false" | bash; \
    # 确保SDKMAN目录存在
    [ -d "${SDKMAN_DIR}" ] || mkdir -p "${SDKMAN_DIR}"; \
    # 确保初始化脚本存在
    [ -f "${SDKMAN_DIR}/bin/sdkman-init.sh" ] || { echo "SDKMAN安装失败"; exit 1; }; \
    # 配置SDKMAN
    mkdir -p "${SDKMAN_DIR}/etc"; \
    echo "sdkman_auto_answer=true" > "${SDKMAN_DIR}/etc/config"; \
    echo "sdkman_auto_selfupdate=false" >> "${SDKMAN_DIR}/etc/config"; \
    echo "sdkman_curl_connect_timeout=30" >> "${SDKMAN_DIR}/etc/config"; \
    echo "sdkman_curl_max_time=60" >> "${SDKMAN_DIR}/etc/config"; \
    # 添加SDKMAN初始化到shell配置文件（安装阶段不启用离线模式）
    echo 'source "${SDKMAN_DIR}/bin/sdkman-init.sh"' >> /root/.bashrc; \
    # 创建sdk命令的全局别名（安装阶段保持在线模式）
    echo '#!/bin/bash' > /usr/local/bin/sdk && \
    echo 'source "${SDKMAN_DIR}/bin/sdkman-init.sh" && sdk "$@"' >> /usr/local/bin/sdk && \
    chmod +x /usr/local/bin/sdk; \
    # 初始化SDKMAN并验证安装
    bash -c "source ${SDKMAN_DIR}/bin/sdkman-init.sh && sdk version"

# 安装JDK和Ant
RUN set -eux; \
    bash -c "source ${SDKMAN_DIR}/bin/sdkman-init.sh && \
    # 安装JDK \
    sdk install java 8.0.392-tem && \
    # 安装Ant \
    sdk install ant 1.9.7 && \
    sdk install ant 1.10.1 && \
    sdk install ant 1.10.3 && \
    sdk install ant 1.10.13 && \
    # 设置默认版本 \
    sdk default java 8.0.392-tem && \
    sdk default ant 1.10.13 && \
    # 清理不必要的文件以减小镜像大小 \
    if [ -d \"${SDKMAN_DIR}/archives\" ]; then find ${SDKMAN_DIR}/archives -name '*.zip' -delete; fi && \
    if [ -d \"${SDKMAN_DIR}/tmp\" ]; then find ${SDKMAN_DIR}/tmp -type f -delete; fi && \
    # 验证安装 \
    java -version && \
    ant -version"

# 在安装完成后配置离线模式
RUN set -eux; \
    # 在配置文件中设置离线模式 \
    echo 'sdkman_offline_mode=true' >> ${SDKMAN_DIR}/etc/config && \
    # 重新配置.bashrc，添加自动启用离线模式 \
    echo '# 自动启用SDKMAN离线模式（仅在安装完成后）' >> /root/.bashrc && \
    echo 'if command -v sdk >/dev/null 2>&1; then' >> /root/.bashrc && \
    echo '  sdk offline enable >/dev/null 2>&1' >> /root/.bashrc && \
    echo 'fi' >> /root/.bashrc && \
    # 重新配置全局sdk命令，确保离线模式生效 \
    echo '#!/bin/bash' > /usr/local/bin/sdk && \
    echo 'source "${SDKMAN_DIR}/bin/sdkman-init.sh"' >> /usr/local/bin/sdk && \
    echo '# 自动启用离线模式（除非是offline命令本身）' >> /usr/local/bin/sdk && \
    echo 'if [[ "$1" != "offline" ]]; then' >> /usr/local/bin/sdk && \
    echo '  sdk offline enable >/dev/null 2>&1' >> /usr/local/bin/sdk && \
    echo 'fi' >> /usr/local/bin/sdk && \
    echo 'sdk "$@"' >> /usr/local/bin/sdk && \
    chmod +x /usr/local/bin/sdk

# 设置工作目录
WORKDIR /app

# 添加切换版本的便捷脚本
COPY <<EOF /usr/local/bin/versions
#!/bin/bash
source "${SDKMAN_DIR}/bin/sdkman-init.sh"
sdk offline enable >/dev/null 2>&1
echo "Java版本:"
echo "  java 8.0.392-tem    # Java 8"
echo "切换Ant版本:"
echo "  sdk use ant 1.9.7    # Ant 1.9.7"
echo "  sdk use ant 1.10.1   # Ant 1.10.1"
echo "  sdk use ant 1.10.3   # Ant 1.10.3"
echo "  sdk use ant 1.10.13  # Ant 1.10.13"
echo "当前版本:"
java -version
ant -version
EOF
RUN chmod +x /usr/local/bin/versions

# 设置入口点
COPY <<EOF /usr/local/bin/entrypoint.sh
#!/bin/bash
# 初始化SDKMAN并设置离线模式
source "${SDKMAN_DIR}/bin/sdkman-init.sh"
sdk offline enable >/dev/null 2>&1

versions

# 执行传入的命令或启动bash
if [ \$# -eq 0 ]; then
  exec /bin/bash
else
  exec "\$@"
fi
EOF

RUN chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD [] 
